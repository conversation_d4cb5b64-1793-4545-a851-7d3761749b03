<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RK Player</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>520</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="1" column="0">
     <widget class="QWidget" name="videoWidget" native="true">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="currentTimeLabel">
         <property name="minimumSize">
          <size>
           <width>50</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string>00:00</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QPushButton" name="rewindButton">
         <property name="minimumSize">
          <size>
           <width>40</width>
           <height>35</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>40</width>
           <height>35</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>14</pointsize>
          </font>
         </property>
         <property name="text">
          <string>⏪</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QPushButton" name="playPauseButton">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>35</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>35</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>16</pointsize>
          </font>
         </property>
         <property name="text">
          <string>▶️</string>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QPushButton" name="fastForwardButton">
         <property name="minimumSize">
          <size>
           <width>40</width>
           <height>35</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>40</width>
           <height>35</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>14</pointsize>
          </font>
         </property>
         <property name="text">
          <string>⏩</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QSlider" name="progressSlider">
         <property name="minimum">
          <number>0</number>
         </property>
         <property name="maximum">
          <number>100</number>
         </property>
         <property name="value">
          <number>0</number>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="QLabel" name="totalTimeLabel">
         <property name="minimumSize">
          <size>
           <width>50</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string>00:00</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
