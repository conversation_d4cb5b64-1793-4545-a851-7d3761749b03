#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QKeyEvent>
#include <QMouseEvent>
#include <gst/gst.h>

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onPlayPauseClicked();
    void onRewindClicked();
    void onFastForwardClicked();
    void onExitClicked();
    void onProgressSliderPressed();
    void onProgressSliderReleased();
    void onProgressSliderValueChanged(int value);
    void updatePosition();

private:
    Ui::MainWindow *ui;

    // GStreamer相关
    GstElement *pipeline;
    GstElement *playbin;
    GstBus *bus;

    // 定时器用于更新播放进度
    QTimer *positionTimer;

    // 控制条隐藏定时器
    QTimer *hideControlsTimer;

    // 播放状态
    bool isPlaying;
    bool isSliderPressed;
    gint64 duration;
    bool controlsVisible;

    // 私有方法
    void initializeGStreamer();
    void setupConnections();
    void loadVideo(const QString &filePath);
    void updateTimeLabel(gint64 current, gint64 total);
    QString formatTime(gint64 time);
    void seek(gint64 position);
    void showControls();
    void hideControls();
    void resetHideTimer();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
};
#endif // MAINWINDOW_H
