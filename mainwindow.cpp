#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QDebug>
#include <QApplication>
#include <QDir>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , pipeline(nullptr)
    , source(nullptr)
    , decoder(nullptr)
    , videoconvert(nullptr)
    , videosink(nullptr)
    , progressTimer(new QTimer(this))
    , isPlaying(false)
    , isSliderPressed(false)
    , duration(0)
    , position(0)
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    showMaximized();

    initializeGStreamer();
    setupConnections();

    // Load the video file
    loadVideo("/tmp/recordings/recording_2021-01-01_13-00-12.mp4");
}

MainWindow::~MainWindow()
{
    if (pipeline) {
        gst_element_set_state(pipeline, GST_STATE_NULL);
        gst_object_unref(pipeline);
    }
    delete ui;
}

void MainWindow::initializeGStreamer()
{
    // Initialize GStreamer
    gst_init(nullptr, nullptr);

    // Create pipeline elements
    pipeline = gst_pipeline_new("video-player");
    source = gst_element_factory_make("filesrc", "file-source");
    decoder = gst_element_factory_make("decodebin", "decoder");
    videoconvert = gst_element_factory_make("videoconvert", "converter");
    videosink = gst_element_factory_make("ximagesink", "video-output");

    if (!pipeline || !source || !decoder || !videoconvert || !videosink) {
        qDebug() << "Failed to create GStreamer elements";
        return;
    }

    // Add elements to pipeline
    gst_bin_add_many(GST_BIN(pipeline), source, decoder, videoconvert, videosink, nullptr);

    // Link source to decoder
    if (!gst_element_link(source, decoder)) {
        qDebug() << "Failed to link source and decoder";
        return;
    }

    // Link videoconvert to videosink
    if (!gst_element_link(videoconvert, videosink)) {
        qDebug() << "Failed to link videoconvert and videosink";
        return;
    }

    // Connect decoder pad-added signal
    g_signal_connect(decoder, "pad-added", G_CALLBACK(+[](GstElement *element, GstPad *pad, gpointer data) {
        MainWindow *window = static_cast<MainWindow*>(data);
        GstPad *sinkpad = gst_element_get_static_pad(window->videoconvert, "sink");
        if (!gst_pad_is_linked(sinkpad)) {
            gst_pad_link(pad, sinkpad);
        }
        gst_object_unref(sinkpad);
    }), this);

    // Set video overlay
    gst_video_overlay_set_window_handle(GST_VIDEO_OVERLAY(videosink),
                                       (guintptr)ui->videoWidget->winId());

    // Setup bus callback
    GstBus *bus = gst_element_get_bus(pipeline);
    gst_bus_add_watch(bus, busCallback, this);
    gst_object_unref(bus);

    // Setup progress timer
    progressTimer->setInterval(100); // Update every 100ms
    connect(progressTimer, &QTimer::timeout, this, &MainWindow::updateProgress);
}
